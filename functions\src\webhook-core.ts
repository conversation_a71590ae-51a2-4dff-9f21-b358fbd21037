// Core webhook functions for Hive Campus - Minimal and focused
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

// Helper function to generate 6-digit secret code
function generateSecretCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Core Stripe webhook handler - focused only on payment completion
export const coreStripeWebhook = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('🔗 Core webhook received');

      if (req.method !== 'POST') {
        res.status(405).send('Method not allowed');
        return;
      }

      const event = req.body;
      console.log(`📨 Processing event: ${event.type}`);

      // Handle checkout session completed
      if (event.type === 'checkout.session.completed') {
        await handleCheckoutCompleted(event.data.object);
      }

      // Handle payment intent succeeded  
      if (event.type === 'payment_intent.succeeded') {
        await handlePaymentSucceeded(event.data.object);
      }

      res.status(200).json({ received: true, processed: true });

    } catch (error) {
      console.error('❌ Core webhook error:', error);
      res.status(500).send(`Webhook failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  });

// Handle checkout session completed
async function handleCheckoutCompleted(session: any): Promise<void> {
  try {
    console.log('💳 Processing checkout completed:', session.id);

    const metadata = session.metadata;
    if (!metadata?.orderId) {
      console.log('⚠️ No order ID in metadata');
      return;
    }

    const orderId = metadata.orderId;
    console.log(`📦 Processing order: ${orderId}`);

    // Get order from Firestore
    const orderRef = admin.firestore().collection('orders').doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      console.error(`❌ Order not found: ${orderId}`);
      return;
    }

    const orderData = orderDoc.data();
    
    // Generate 6-digit secret code
    const secretCode = generateSecretCode();
    console.log(`🔐 Generated secret code: ${secretCode}`);

    // Update order with payment completion and secret code
    await orderRef.update({
      status: 'payment_completed',
      stripeSessionId: session.id,
      stripePaymentIntentId: session.payment_intent,
      secretCode: secretCode,
      paymentCompletedAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log(`✅ Order ${orderId} updated with secret code`);

    // Update listing status to sold
    if (orderData?.listingId) {
      await admin.firestore().collection('listings').doc(orderData.listingId).update({
        status: 'sold',
        soldAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
      console.log(`✅ Listing ${orderData.listingId} marked as sold`);
    }

    // Send notifications
    await sendNotifications(orderData, orderId, secretCode);

    console.log(`✅ Checkout completed processing finished`);

  } catch (error) {
    console.error('❌ Error in handleCheckoutCompleted:', error);
    throw error;
  }
}

// Handle payment intent succeeded
async function handlePaymentSucceeded(paymentIntent: any): Promise<void> {
  try {
    console.log('💰 Processing payment succeeded:', paymentIntent.id);

    const metadata = paymentIntent.metadata;
    if (!metadata?.orderId) {
      console.log('⚠️ No order ID in payment intent metadata');
      return;
    }

    const orderId = metadata.orderId;
    
    // Get order from Firestore
    const orderRef = admin.firestore().collection('orders').doc(orderId);
    const orderDoc = await orderRef.get();

    if (!orderDoc.exists) {
      console.error(`❌ Order not found: ${orderId}`);
      return;
    }

    // Update order status
    await orderRef.update({
      status: 'in_progress',
      stripePaymentIntentId: paymentIntent.id,
      paymentSucceededAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Update listing status to sold
    if (metadata.listingId) {
      await admin.firestore().collection('listings').doc(metadata.listingId).update({
        status: 'sold',
        soldAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });
      console.log(`✅ Listing ${metadata.listingId} marked as sold`);
    }

    console.log(`✅ Payment succeeded processing finished`);

  } catch (error) {
    console.error('❌ Error in handlePaymentSucceeded:', error);
    throw error;
  }
}

// Send basic notifications
async function sendNotifications(orderData: any, orderId: string, secretCode: string): Promise<void> {
  try {
    // Notify buyer
    if (orderData?.buyerId) {
      await admin.firestore().collection('notifications').add({
        userId: orderData.buyerId,
        type: 'payment_success',
        title: 'Payment Successful!',
        message: `Your payment has been processed. Secret code: ${secretCode}`,
        orderId: orderId,
        secretCode: secretCode,
        read: false,
        createdAt: admin.firestore.Timestamp.now()
      });
    }

    // Notify seller
    if (orderData?.sellerId) {
      await admin.firestore().collection('notifications').add({
        userId: orderData.sellerId,
        type: 'order_received',
        title: 'New Order Received!',
        message: `You have received a new order for "${orderData.listingTitle}"`,
        orderId: orderId,
        read: false,
        createdAt: admin.firestore.Timestamp.now()
      });
    }

    console.log(`✅ Notifications sent for order: ${orderId}`);
  } catch (error) {
    console.error('❌ Error sending notifications:', error);
  }
}

// Test function
export const testCoreWebhook = functions
  .runWith({
    memory: '128MB',
    timeoutSeconds: 10
  })
  .https.onRequest(async (_req, res) => {
    const testCode = generateSecretCode();
    res.json({
      success: true,
      message: 'Core webhook functions working',
      testSecretCode: testCode,
      timestamp: new Date().toISOString()
    });
  });
